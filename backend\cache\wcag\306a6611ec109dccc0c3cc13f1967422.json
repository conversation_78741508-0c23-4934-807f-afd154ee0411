{"data": [{"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(2)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(2)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 1, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(3)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(3)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 2, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(4)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(4)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 3, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(5)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(5)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 4, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(6)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(6)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 5, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(7)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(7)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 6, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(8)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(8)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 7, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.321Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(9)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(9)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 8, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(10)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(10)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 9, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(11)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(11)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 10, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(12)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(12)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 11, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(13)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(13)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 12, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(14)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(14)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 13, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(15)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(15)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 14, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(16)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(16)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 15, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(17)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(17)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 16, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(18)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(18)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 17, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(19)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(19)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 18, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(20)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(20)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 19, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}, {"type": "code", "description": "Skip link with issues: Skip link: \"\" -> ", "value": "Skip link", "selector": "[class*=\"skip\"]:nth-of-type(21)", "elementCount": 1, "affectedSelectors": ["[class*=\"skip\"]:nth-of-type(21)", "<PERSON><PERSON>", "link"], "severity": "error", "metadata": {"scanDuration": 2142, "elementsAnalyzed": 27, "checkSpecificData": {"automationRate": 0.95, "checkType": "skip-link-analysis", "skipLinkDetection": true, "bypassMechanismAnalysis": true, "aiSemanticValidation": true, "accessibilityPatterns": true, "evidenceIndex": 20, "ruleId": "WCAG-047", "ruleName": "Skip <PERSON>s", "timestamp": "2025-07-12T05:47:51.322Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.7999999999999999, "qualityMetricsReliability": 0.9, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752299271328, "hash": "48195d69c54a670cd955e371979730d6", "accessCount": 1, "lastAccessed": 1752299271328, "size": 15516, "metadata": {"originalKey": "WCAG-047:WCAG-047", "normalizedKey": "wcag-047_wcag-047", "savedAt": 1752299271329, "version": "1.1", "keyHash": "417eebaf68cbb128db8b2479b38fe379"}}