{"data": [{"type": "code", "description": "Moving content without user controls: JavaScript-based animation detected", "value": "Moving content detected", "selector": "script", "severity": "error", "elementCount": 1, "affectedSelectors": ["script", "Moving", "content", "detected"], "metadata": {"scanDuration": 46, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.75, "checkType": "motion-analysis", "mediaElementsAnalyzed": true, "animationDetection": true, "accessibilityPatterns": true, "multimediaAccessibilityTesting": true, "evidenceIndex": 0, "ruleId": "WCAG-045", "ruleName": "Pause, Stop, Hide", "timestamp": "2025-07-12T05:43:59.664Z", "qualityMetricsScore": 1, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.8, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752299039664, "hash": "8e8a05242418ea6679a24f17f77e0c23", "accessCount": 1, "lastAccessed": 1752299039664, "size": 794, "metadata": {"originalKey": "WCAG-045:WCAG-045", "normalizedKey": "wcag-045_wcag-045", "savedAt": 1752299039664, "version": "1.1", "keyHash": "88680d5643c69ef38cb120d07f03a4fb"}}