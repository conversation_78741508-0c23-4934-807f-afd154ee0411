{"data": [{"type": "text", "description": "Technical error during check execution", "value": "Attempted to use detached Frame 'D9DFA1037067F29D763E9B77766CAB7B'.", "severity": "error", "elementCount": 1, "affectedSelectors": ["Attempted", "to", "use", "detached", "<PERSON>ame", "D9DFA1037067F29D763E9B77766CAB7B"], "metadata": {"scanDuration": 1, "elementsAnalyzed": 1, "checkSpecificData": {"automationRate": 0.8, "checkType": "language-detection-analysis", "languagePatternAnalysis": true, "langAttributeValidation": true, "aiLanguageDetection": true, "contentQualityAnalysis": true, "evidenceIndex": 0, "ruleId": "WCAG-038", "ruleName": "Language of Parts", "timestamp": "2025-07-12T05:48:26.140Z", "qualityMetricsScore": 0.9, "qualityMetricsCompleteness": 0.8999999999999999, "qualityMetricsReliability": 0.6, "thirdPartyValidation": true, "advancedSelectors": true, "contextAnalysis": true}}}], "timestamp": 1752299306140, "hash": "1f2c39f19e00900234d7314acfeb5447", "accessCount": 1, "lastAccessed": 1752299306140, "size": 830, "metadata": {"originalKey": "WCAG-038:WCAG-038", "normalizedKey": "wcag-038_wcag-038", "savedAt": 1752299306141, "version": "1.1", "keyHash": "6c47ba9a25463f9fa47406d8d40b8da7"}}