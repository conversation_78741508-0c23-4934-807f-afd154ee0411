{"data": {"ruleId": "WCAG-064", "ruleName": "Change on Request", "category": "predictable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AAA", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0305, "automated": true, "evidence": [{"type": "code", "description": "Context change detection: high risk", "value": "Unexpected: 24, User-controlled: 0, Automatic: 0, Warnings: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Unexpected", "User-controlled", "Automatic", "Warnings"], "metadata": {"scanDuration": 647, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T05:47:47.095Z"}}}, {"type": "text", "description": "Detected context change types", "value": "new-window", "severity": "info", "elementCount": 1, "affectedSelectors": ["new-window"], "metadata": {"scanDuration": 647, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T05:47:47.095Z"}}}, {"type": "code", "description": "User control validation: Inadequate control mechanisms", "value": "Available controls: none (need at least 2)", "severity": "error", "elementCount": 1, "affectedSelectors": ["Available", "controls", "none", "need", "at", "least"], "metadata": {"scanDuration": 647, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T05:47:47.095Z"}}}, {"type": "text", "description": "Unexpected change analysis: Low risk of unexpected changes", "value": "Risk assessment: low, Score: 0.0%", "severity": "info", "elementCount": 1, "affectedSelectors": ["Risk", "assessment", "low", "Score"], "metadata": {"scanDuration": 647, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T05:47:47.095Z"}}}, {"type": "text", "description": "Accessibility control testing: No context change controls detected", "value": "No context change controls found to test", "severity": "info", "elementCount": 1, "affectedSelectors": ["No", "context", "change", "controls", "found", "to", "test"], "metadata": {"scanDuration": 647, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-064", "ruleName": "Change on Request", "timestamp": "2025-07-12T05:47:47.095Z"}}}], "recommendations": ["Add user control and warnings for context changes", "Add user control mechanisms: confirmations, warnings, or disable options"], "executionTime": 177, "originalScore": 50, "thresholdApplied": 75, "scoringDetails": "50.0% (threshold: 75%) - FAILED"}, "timestamp": 1752299267095, "hash": "273478246d69da3e52d3bd782fde1b37", "accessCount": 1, "lastAccessed": 1752299267095, "size": 2814, "metadata": {"originalKey": "WCAG-064:053b13d2:add92319", "normalizedKey": "wcag-064_053b13d2_add92319", "savedAt": 1752299267095, "version": "1.1", "keyHash": "068b17f4e1323d7fa24ab85adbc3cecc"}}