{"data": {"ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "failed", "score": 0, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "code", "description": "Advanced trap detection: custom trap detected", "value": "Severity: critical, Affected: a:nth-of-type(1), a:nth-of-type(2), a:nth-of-type(3), a:nth-of-type(4), button:nth-of-type(5), a:nth-of-type(9), a:nth-of-type(13), a:nth-of-type(14), a:nth-of-type(15), button:nth-of-type(16), a:nth-of-type(37), button:nth-of-type(38), a:nth-of-type(57), button:nth-of-type(58), a:nth-of-type(83), button:nth-of-type(84), a:nth-of-type(99), a:nth-of-type(102), a:nth-of-type(103), button:nth-of-type(104), button:nth-of-type(105), a:nth-of-type(106), a:nth-of-type(107), a:nth-of-type(108), a:nth-of-type(109), a:nth-of-type(110), a:nth-of-type(111), a:nth-of-type(112), a:nth-of-type(113), a:nth-of-type(114), a:nth-of-type(115), a:nth-of-type(116), a:nth-of-type(117), a:nth-of-type(118), a:nth-of-type(119), a:nth-of-type(120), a:nth-of-type(121), a:nth-of-type(122), a:nth-of-type(123), a:nth-of-type(124), a:nth-of-type(125), a:nth-of-type(126), a:nth-of-type(127), a:nth-of-type(128), a:nth-of-type(129), a:nth-of-type(130), a:nth-of-type(131), a:nth-of-type(132), a:nth-of-type(133), a:nth-of-type(134), a:nth-of-type(135), a:nth-of-type(136), a:nth-of-type(137), a:nth-of-type(138), a:nth-of-type(139), a:nth-of-type(140), a:nth-of-type(141), button:nth-of-type(142), button:nth-of-type(143), button:nth-of-type(144), button:nth-of-type(145), button:nth-of-type(146), button:nth-of-type(147), a:nth-of-type(148), a:nth-of-type(149), a:nth-of-type(150), a:nth-of-type(151), a:nth-of-type(152), a:nth-of-type(153), a:nth-of-type(154), button:nth-of-type(155), button:nth-of-type(156), a:nth-of-type(157), a:nth-of-type(158), a:nth-of-type(159), a:nth-of-type(160), a:nth-of-type(161), a:nth-of-type(162), a:nth-of-type(163), a:nth-of-type(164), a:nth-of-type(165), a:nth-of-type(166), a:nth-of-type(167), a:nth-of-type(168), a:nth-of-type(169), a:nth-of-type(170), a:nth-of-type(171), a:nth-of-type(172), a:nth-of-type(173), button:nth-of-type(174), button:nth-of-type(175), button:nth-of-type(176), button:nth-of-type(177), button:nth-of-type(178), button:nth-of-type(179), a:nth-of-type(180), a:nth-of-type(181), a:nth-of-type(182), a:nth-of-type(183), a:nth-of-type(184), a:nth-of-type(185), a:nth-of-type(186), a:nth-of-type(187), a:nth-of-type(188), a:nth-of-type(189), a:nth-of-type(190), a:nth-of-type(191), a:nth-of-type(192), a:nth-of-type(193), a:nth-of-type(194), a:nth-of-type(195), a:nth-of-type(196), a:nth-of-type(197), a:nth-of-type(198), a:nth-of-type(199), a:nth-of-type(200), a:nth-of-type(201), a:nth-of-type(202), a:nth-of-type(203), a:nth-of-type(204), a:nth-of-type(205), a:nth-of-type(206), a:nth-of-type(207), a:nth-of-type(208), a:nth-of-type(209), a:nth-of-type(210), a:nth-of-type(211), a:nth-of-type(212), a:nth-of-type(213), a:nth-of-type(214), a:nth-of-type(215), a:nth-of-type(216), a:nth-of-type(217), a:nth-of-type(218), a:nth-of-type(219), a:nth-of-type(220), a:nth-of-type(221), a:nth-of-type(222), a:nth-of-type(223), a:nth-of-type(224), a:nth-of-type(225), a:nth-of-type(226), a:nth-of-type(227), a:nth-of-type(228), a:nth-of-type(229), a:nth-of-type(230), a:nth-of-type(231), a:nth-of-type(232), a:nth-of-type(233), a:nth-of-type(234), button:nth-of-type(235), button:nth-of-type(236), a:nth-of-type(237), a:nth-of-type(238), a:nth-of-type(239), a:nth-of-type(240), a:nth-of-type(241), button:nth-of-type(242), Escape routes: 0", "severity": "error", "elementCount": 1, "affectedSelectors": ["Severity", "critical", "Affected", "a", "nth-of-type", "button", "Escape", "routes"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Escape mechanisms validation: Insufficient escape routes", "value": "Only 1/5 escape mechanisms found", "severity": "warning", "elementCount": 1, "affectedSelectors": ["Only", "escape", "mechanisms", "found"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 1 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-0 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-0", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 2, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 2 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-1 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-1", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 3, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 3 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-2 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-2", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 4, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 4 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-3 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-3", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 5, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 5 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-4 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-4", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 6, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 6 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-5 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-5", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 7, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 7 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-6 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-6", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 8, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 8 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-7 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-7", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 9, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 9 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-8 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-8", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 10, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 10 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-9 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-9", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 11, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 11 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-10 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-10", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 12, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 12 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-11 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-11", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 13, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 13 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-12 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-12", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 14, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 14 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-13 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-13", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 15, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 15 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-14 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-14", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 16, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 16 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-15 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-15", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 17, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 17 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-16 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-16", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 18, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 18 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-17 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-17", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 19, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 19 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-18 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-18", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 20, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 20 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-19 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-19", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 21, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 21 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-20 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-20", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 22, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.523Z"}}}, {"type": "code", "description": "Complex widget 22 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-21 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-21", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 23, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 23 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-22 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-22", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 24, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 24 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-23 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-23", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 25, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 25 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-24 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-24", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 26, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 26 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-25 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-25", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 27, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 27 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-26 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-26", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 28, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 28 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-27 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-27", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 29, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 29 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-28 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-28", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 30, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 30 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-29 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-29", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 31, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}, {"type": "code", "description": "Complex widget 31 (tabpanel) needs keyboard navigation improvements", "value": "tabpanel-widget-30 - accessibility score: 0/4, missing: arrow keys escape tab navigation instructions", "severity": "warning", "elementCount": 1, "affectedSelectors": ["tabpanel-widget-30", "accessibility", "score", "missing", "arrow", "keys", "escape", "tab", "navigation", "instructions"], "metadata": {"scanDuration": 2576, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 32, "ruleId": "WCAG-027", "ruleName": "No Keyboard Trap", "timestamp": "2025-07-12T05:47:59.524Z"}}}], "recommendations": ["Add escape mechanisms (Esc key, close button)", "Ensure focus can move freely between elements", "Provide clear instructions for keyboard navigation", "Add multiple escape mechanisms (Esc key, close button, click outside)", "Add proper keyboard navigation to tabpanel widget 1", "Add proper keyboard navigation to tabpanel widget 2", "Add proper keyboard navigation to tabpanel widget 3", "Add proper keyboard navigation to tabpanel widget 4", "Add proper keyboard navigation to tabpanel widget 5", "Add proper keyboard navigation to tabpanel widget 6", "Add proper keyboard navigation to tabpanel widget 7", "Add proper keyboard navigation to tabpanel widget 8", "Add proper keyboard navigation to tabpanel widget 9", "Add proper keyboard navigation to tabpanel widget 10", "Add proper keyboard navigation to tabpanel widget 11", "Add proper keyboard navigation to tabpanel widget 12", "Add proper keyboard navigation to tabpanel widget 13", "Add proper keyboard navigation to tabpanel widget 14", "Add proper keyboard navigation to tabpanel widget 15", "Add proper keyboard navigation to tabpanel widget 16", "Add proper keyboard navigation to tabpanel widget 17", "Add proper keyboard navigation to tabpanel widget 18", "Add proper keyboard navigation to tabpanel widget 19", "Add proper keyboard navigation to tabpanel widget 20", "Add proper keyboard navigation to tabpanel widget 21", "Add proper keyboard navigation to tabpanel widget 22", "Add proper keyboard navigation to tabpanel widget 23", "Add proper keyboard navigation to tabpanel widget 24", "Add proper keyboard navigation to tabpanel widget 25", "Add proper keyboard navigation to tabpanel widget 26", "Add proper keyboard navigation to tabpanel widget 27", "Add proper keyboard navigation to tabpanel widget 28", "Add proper keyboard navigation to tabpanel widget 29", "Add proper keyboard navigation to tabpanel widget 30", "Add proper keyboard navigation to tabpanel widget 31"], "executionTime": 482, "originalScore": 0, "thresholdApplied": 75, "scoringDetails": "0.0% (threshold: 75%) - FAILED"}, "timestamp": 1752299279524, "hash": "4fb01ea44791203037aefe9d7f86d336", "accessCount": 1, "lastAccessed": 1752299279524, "size": 25777, "metadata": {"originalKey": "WCAG-027:053b13d2:add92319", "normalizedKey": "wcag-027_053b13d2_add92319", "savedAt": 1752299279525, "version": "1.1", "keyHash": "b3cb13b8b936a4a98f44be31813353a1"}}