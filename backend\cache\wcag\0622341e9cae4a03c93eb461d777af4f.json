{"data": {"ruleId": "WCAG-029", "ruleName": "Page Titled", "category": "operable", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "A", "status": "passed", "score": 90, "maxScore": 100, "weight": 0.0815, "automated": true, "evidence": [{"type": "info", "description": "Page has descriptive title", "value": "<title>#1 Healthcare Collaboration Platform | TigerConnect</title>", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "Healthcare", "Collaboration", "Platform", "TigerConnect"], "severity": "info", "metadata": {"scanDuration": 443, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-12T05:48:00.626Z"}}}, {"type": "warning", "description": "Multiple title elements found", "value": "14 title elements detected", "selector": "title", "elementCount": 1, "affectedSelectors": ["title", "elements", "detected"], "severity": "warning", "metadata": {"scanDuration": 443, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 1, "ruleId": "WCAG-029", "ruleName": "Page Titled", "timestamp": "2025-07-12T05:48:00.626Z"}}}], "recommendations": ["Use exactly one title element per page", "Use descriptive titles that identify the page topic and purpose", "Include both page-specific and site context in titles", "Keep titles between 10-60 characters for optimal display", "Test titles with screen readers and browser tabs"], "executionTime": 6, "originalScore": 90, "thresholdApplied": 75, "scoringDetails": "90.0% (threshold: 75%) - PASSED"}, "timestamp": 1752299280626, "hash": "10c59df68c9c874290680227293710e9", "accessCount": 1, "lastAccessed": 1752299280626, "size": 1570, "metadata": {"originalKey": "WCAG-029:053b13d2:add92319", "normalizedKey": "wcag-029_053b13d2_add92319", "savedAt": 1752299280627, "version": "1.1", "keyHash": "a2e558d259f158ebff8bfc0e8ff98394"}}