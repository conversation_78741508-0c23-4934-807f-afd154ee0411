{"data": {"ruleId": "WCAG-057", "ruleName": "Status Messages", "category": "robust", "wcagVersion": "3.0", "successCriterion": "Unknown", "level": "AA", "status": "passed", "score": 100, "maxScore": 100, "weight": 0.0458, "automated": true, "evidence": [{"type": "interaction", "description": "Status messages accessibility analysis", "value": "Found 4 status elements and 0 potential status messages", "elementCount": 1, "affectedSelectors": ["Found", "status", "elements", "and", "potential", "messages"], "severity": "info", "metadata": {"scanDuration": 82, "elementsAnalyzed": 0, "checkSpecificData": {"axeValidationEnabled": false, "enhancedAccuracy": false, "evidenceIndex": 0, "ruleId": "WCAG-057", "ruleName": "Status Messages", "timestamp": "2025-07-12T05:47:48.589Z"}}}], "recommendations": ["Add role=\"alert\" and aria-live=\"assertive\" to error messages", "Add role=\"status\" and aria-live=\"polite\" to success and info messages", "Use aria-atomic=\"true\" for messages that should be read completely", "Ensure status messages are programmatically determinable"], "executionTime": 22, "originalScore": 100, "thresholdApplied": 75, "scoringDetails": "100.0% (threshold: 75%) - PASSED"}, "timestamp": 1752299268589, "hash": "0e146d17517f820cf32748280b232353", "accessCount": 1, "lastAccessed": 1752299268589, "size": 1121, "metadata": {"originalKey": "WCAG-057:053b13d2:add92319", "normalizedKey": "wcag-057_053b13d2_add92319", "savedAt": 1752299268590, "version": "1.1", "keyHash": "51044a30d5f4e048dc6369d55d1dc00d"}}